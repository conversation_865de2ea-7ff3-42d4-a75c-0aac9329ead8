import Message from 'tdesign-miniprogram/message/index';
import request from '~/api/request';
import { isTeacher, getCurrentUser } from '~/utils/auth';

Page({
  data: {
    artworkId: null,
    artwork: null,
    images: [],
    aiEvaluation: null,
    evaluationScore: null,
    teacherEvaluations: [],
    loading: true,
    // 教师评价相关
    isTeacher: false,
    currentUserId: null,
    hasUserEvaluated: false,
    teacherEvaluationsCollapsed: false,
    // 添加评价弹窗
    showAddEvaluationModal: false,
    submitting: false,
    newEvaluation: {
      overallScore: 80,
      technicalAnalysis: '',
      problemDiagnosis: '',
      improvementPlan: ''
    }
  },

  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ artworkId: id });
      this.initUserInfo();
      this.loadArtworkDetail(id);
    } else {
      this.showError('作品ID不存在');
    }
  },

  // 初始化用户信息
  initUserInfo() {
    const currentUser = getCurrentUser();
    const userIsTeacher = isTeacher();

    this.setData({
      isTeacher: userIsTeacher,
      currentUserId: currentUser?.id || null
    });
  },

  async loadArtworkDetail(artworkId) {
    try {
      wx.showLoading({ title: '加载中...' });

      const res = await request(`/artwork/${artworkId}`, 'GET');

      if (!res || (res.code !== 0 && res.code !== 200)) {
        this.showError(res?.msg || res?.message || '加载失败');
        return;
      }

      const { artwork, evaluations, score } = res.data;
      
      // 解析图片数组
      let images = [];
      if (artwork.images) {
        try {
          images = JSON.parse(artwork.images);
        } catch (e) {
          console.error('解析图片数组失败:', e);
          images = [artwork.images]; // 如果解析失败，当作单张图片处理
        }
      }
      
      // 分离AI评价和教师评价
      let aiEvaluation = null;
      let teacherEvaluations = [];
      
      if (evaluations && evaluations.length > 0) {
        evaluations.forEach(evaluation => {
          if (evaluation.type === 1) {
            aiEvaluation = evaluation;
          } else if (evaluation.type === 2) {
            teacherEvaluations.push(evaluation);
          }
        });
      }
      
      // 处理评分数据，添加兜底逻辑
      let processedScore = null;
      if (score) {
        processedScore = {
          perspectiveScore: score.perspectiveScore || this.calculateDefaultScore(aiEvaluation?.overallScore, 0.18),
          proportionScore: score.proportionScore || this.calculateDefaultScore(aiEvaluation?.overallScore, 0.20),
          lightShadowScore: score.lightShadowScore || this.calculateDefaultScore(aiEvaluation?.overallScore, 0.20),
          lineQualityScore: score.lineQualityScore || this.calculateDefaultScore(aiEvaluation?.overallScore, 0.22),
          overallEffectScore: score.overallEffectScore || this.calculateDefaultScore(aiEvaluation?.overallScore, 0.20)
        };
      } else if (aiEvaluation?.overallScore) {
        // 如果没有详细评分但有总分，按比例分配
        const totalScore = parseFloat(aiEvaluation.overallScore);
        processedScore = {
          perspectiveScore: Math.round(totalScore * 0.18),
          proportionScore: Math.round(totalScore * 0.20),
          lightShadowScore: Math.round(totalScore * 0.20),
          lineQualityScore: Math.round(totalScore * 0.22),
          overallEffectScore: Math.round(totalScore * 0.20)
        };
      }

      // 检查当前用户是否已评价
      const currentUserId = this.data.currentUserId;
      const hasUserEvaluated = teacherEvaluations.some(evaluation =>
        evaluation.evaluatorId === currentUserId
      );

      // 处理教师评价数据，添加教师信息
      const processedTeacherEvaluations = await this.processTeacherEvaluations(teacherEvaluations);

      this.setData({
        artwork,
        images,
        aiEvaluation,
        evaluationScore: processedScore,
        teacherEvaluations: processedTeacherEvaluations,
        hasUserEvaluated,
        loading: false
      });
      
    } catch (error) {
      console.error('加载作品详情失败:', error);
      this.showError('加载失败，请重试');
    } finally {
      wx.hideLoading();
    }
  },

  // 计算默认分数
  calculateDefaultScore(totalScore, ratio) {
    if (!totalScore) return 15; // 默认15分
    const score = Math.round(parseFloat(totalScore) * ratio);
    return Math.max(10, Math.min(20, score)); // 确保在10-20分范围内
  },

  // 处理教师评价数据，添加教师信息
  async processTeacherEvaluations(evaluations) {
    const processedEvaluations = [];

    for (const evaluation of evaluations) {
      try {
        // 获取教师信息
        const teacherInfo = await this.getTeacherInfo(evaluation.evaluatorId);

        processedEvaluations.push({
          ...evaluation,
          teacherName: teacherInfo?.nickname || '匿名教师',
          teacherAvatar: teacherInfo?.avatar || '/static/default-teacher-avatar.png',
          teacherTitle: '专业教师', // 可以从教师认证信息中获取
          likeCount: 0 // 后续可以添加点赞功能
        });
      } catch (error) {
        console.error('获取教师信息失败:', error);
        processedEvaluations.push({
          ...evaluation,
          teacherName: '匿名教师',
          teacherAvatar: '/static/default-teacher-avatar.png',
          teacherTitle: '专业教师',
          likeCount: 0
        });
      }
    }

    return processedEvaluations;
  },

  // 获取教师信息
  async getTeacherInfo(teacherId) {
    if (!teacherId) return null;

    try {
      const res = await request(`/user/${teacherId}`, 'GET');
      if (res && res.code === 0) {
        return res.data;
      }
    } catch (error) {
      console.error('获取教师信息失败:', error);
    }

    return null;
  },

  // 预览图片
  previewImage(e) {
    const { url } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: this.data.images
    });
  },

  // 切换教师评价展开/收起
  toggleTeacherEvaluations() {
    this.setData({
      teacherEvaluationsCollapsed: !this.data.teacherEvaluationsCollapsed
    });
  },

  // 显示添加评价弹窗
  showAddEvaluationModal() {
    if (!this.data.isTeacher) {
      this.showError('只有教师可以添加评价');
      return;
    }

    if (this.data.hasUserEvaluated) {
      this.showError('您已经评价过此作品');
      return;
    }

    this.setData({
      showAddEvaluationModal: true,
      newEvaluation: {
        overallScore: 80,
        technicalAnalysis: '',
        problemDiagnosis: '',
        improvementPlan: ''
      }
    });
  },

  // 隐藏添加评价弹窗
  hideAddEvaluationModal() {
    this.setData({
      showAddEvaluationModal: false
    });
  },

  // 弹窗显示状态变化
  onAddEvaluationModalChange(e) {
    this.setData({
      showAddEvaluationModal: e.detail.visible
    });
  },

  // 评分变化
  onScoreChange(e) {
    this.setData({
      'newEvaluation.overallScore': e.detail.value
    });
  },

  // 技术分析输入
  onTechnicalAnalysisChange(e) {
    this.setData({
      'newEvaluation.technicalAnalysis': e.detail.value
    });
  },

  // 问题诊断输入
  onProblemDiagnosisChange(e) {
    this.setData({
      'newEvaluation.problemDiagnosis': e.detail.value
    });
  },

  // 改进建议输入
  onImprovementPlanChange(e) {
    this.setData({
      'newEvaluation.improvementPlan': e.detail.value
    });
  },

  // 提交评价
  async submitEvaluation() {
    const { newEvaluation, artworkId, currentUserId } = this.data;

    // 验证输入
    if (!newEvaluation.technicalAnalysis.trim()) {
      this.showError('请填写技术分析');
      return;
    }

    if (!newEvaluation.problemDiagnosis.trim()) {
      this.showError('请填写问题诊断');
      return;
    }

    if (!newEvaluation.improvementPlan.trim()) {
      this.showError('请填写改进建议');
      return;
    }

    this.setData({ submitting: true });

    try {
      const evaluationData = {
        artworkId,
        evaluatorId: currentUserId,
        type: 2, // 教师评价
        technicalAnalysis: newEvaluation.technicalAnalysis.trim(),
        problemDiagnosis: newEvaluation.problemDiagnosis.trim(),
        improvementPlan: newEvaluation.improvementPlan.trim(),
        overallScore: newEvaluation.overallScore
      };

      const res = await request('/evaluation/teacher', 'POST', evaluationData);

      if (res && res.code === 0) {
        this.showSuccess('评价提交成功');
        this.hideAddEvaluationModal();
        // 重新加载作品详情
        this.loadArtworkDetail(artworkId);
      } else {
        this.showError(res?.msg || '提交失败，请重试');
      }
    } catch (error) {
      console.error('提交评价失败:', error);
      this.showError('提交失败，请重试');
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 点赞评价
  async likeEvaluation(e) {
    const { id } = e.currentTarget.dataset;
    // TODO: 实现点赞功能
    console.log('点赞评价:', id);
  },

  // 编辑评价
  editEvaluation(e) {
    const { id } = e.currentTarget.dataset;
    // TODO: 实现编辑功能
    console.log('编辑评价:', id);
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '';

    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;

    // 小于1分钟
    if (diff < 60000) {
      return '刚刚';
    }

    // 小于1小时
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }

    // 小于1天
    if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    }

    // 小于7天
    if (diff < 604800000) {
      return Math.floor(diff / 86400000) + '天前';
    }

    // 超过7天显示具体日期
    return time.toLocaleDateString();
  },

  // 显示错误信息
  showError(message) {
    Message.error({
      context: this,
      offset: [120, 32],
      duration: 3000,
      content: message,
    });
  },

  // 显示成功信息
  showSuccess(message) {
    Message.success({
      context: this,
      offset: [120, 32],
      duration: 3000,
      content: message,
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: `查看作品：${this.data.artwork?.title || '美院作品'}`,
      path: `/pages/artwork-detail/index?id=${this.data.artworkId}`
    };
  }
});
