package com.meiyuan.artevaluation.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.Artwork;
import com.meiyuan.artevaluation.entity.Evaluation;
import com.meiyuan.artevaluation.entity.EvaluationScore;
import com.meiyuan.artevaluation.mapper.EvaluationMapper;
import com.meiyuan.artevaluation.mapper.EvaluationScoreMapper;
import com.meiyuan.artevaluation.service.VolcengineAiAnalysisService;
import com.meiyuan.artevaluation.service.ArtworkService;
import com.meiyuan.artevaluation.service.EvaluationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评价服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EvaluationServiceImpl implements EvaluationService {

    private final EvaluationMapper evaluationMapper;
    private final EvaluationScoreMapper evaluationScoreMapper;
    private final VolcengineAiAnalysisService volcengineAiAnalysisService;
    private final ArtworkService artworkService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAiEvaluation(Long artworkId, String imageUrl) {
        JSONObject aiResult = null;

        try {
            // 调用火山方舟AI分析服务
            aiResult = volcengineAiAnalysisService.analyzeSketch(imageUrl);
            log.info("AI分析成功，作品ID: {}", artworkId);
        } catch (Exception e) {
            log.error("AI分析失败，使用默认评价，作品ID: {}, 错误: {}", artworkId, e.getMessage());
            // 创建默认的AI评价结果
            aiResult = createDefaultAiResult();
        }
        
        // 创建评价记录
        Evaluation evaluation = new Evaluation();
        evaluation.setArtworkId(artworkId);
        evaluation.setType(1); // AI评价
        
        // 设置评价内容
        evaluation.setTechnicalAnalysis(aiResult.getString("technicalAnalysis"));
        evaluation.setProblemDiagnosis(aiResult.getString("problemDiagnosis"));
        evaluation.setImprovementPlan(aiResult.getString("improvementPlan"));
        evaluation.setStageGoals(aiResult.getString("stageGoals"));
        evaluation.setReferenceMaterials(aiResult.getString("referenceMaterials"));
        
        // 获取总分和评分详情
        Map<String, Integer> scores = (Map<String, Integer>) aiResult.get("scores");
        if (scores != null) {
            // 保存评分JSON
            evaluation.setScores(com.alibaba.fastjson.JSON.toJSONString(scores));

            // 计算总分
            int totalScore = scores.values().stream().mapToInt(Integer::intValue).sum();
            evaluation.setOverallScore(new BigDecimal(totalScore));
        }

        // 保存评价
        evaluationMapper.insert(evaluation);

        // 创建评分详情
        if (scores != null && !scores.isEmpty()) {
            EvaluationScore evaluationScore = new EvaluationScore();
            evaluationScore.setEvaluationId(evaluation.getId());

            // 设置各项分数，确保字段名匹配
            if (scores.containsKey("perspectiveScore")) {
                evaluationScore.setPerspectiveScore(new BigDecimal(scores.get("perspectiveScore")));
                log.info("设置透视分数: {}", scores.get("perspectiveScore"));
            }
            if (scores.containsKey("proportionScore")) {
                evaluationScore.setProportionScore(new BigDecimal(scores.get("proportionScore")));
                log.info("设置比例分数: {}", scores.get("proportionScore"));
            }
            if (scores.containsKey("lightShadowScore")) {
                evaluationScore.setLightShadowScore(new BigDecimal(scores.get("lightShadowScore")));
                log.info("设置明暗分数: {}", scores.get("lightShadowScore"));
            }
            if (scores.containsKey("lineQualityScore")) {
                evaluationScore.setLineQualityScore(new BigDecimal(scores.get("lineQualityScore")));
                log.info("设置线条分数: {}", scores.get("lineQualityScore"));
            }
            if (scores.containsKey("overallEffectScore")) {
                evaluationScore.setOverallEffectScore(new BigDecimal(scores.get("overallEffectScore")));
                log.info("设置整体分数: {}", scores.get("overallEffectScore"));
            }

            // 保存评分详情
            evaluationScoreMapper.insert(evaluationScore);
            log.info("评分详情保存成功，评价ID: {}", evaluation.getId());
        } else {
            log.warn("评分数据为空，跳过评分详情保存");
        }
        
        // 更新作品状态为已评价
        artworkService.updateArtworkStatus(artworkId, 2);
        
        return evaluation.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTeacherEvaluation(Evaluation evaluation, EvaluationScore evaluationScore) {
        // 设置类型为教师评价
        evaluation.setType(2);
        
        // 保存评价
        evaluationMapper.insert(evaluation);
        
        // 保存评分详情
        if (evaluationScore != null) {
            evaluationScore.setEvaluationId(evaluation.getId());
            evaluationScoreMapper.insert(evaluationScore);
        }
        
        // 更新作品状态为已评价
        artworkService.updateArtworkStatus(evaluation.getArtworkId(), 2);
        
        return evaluation.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTeacherEvaluation(Evaluation evaluation, EvaluationScore evaluationScore) {
        try {
            // 更新评价
            int evaluationResult = evaluationMapper.updateById(evaluation);

            // 更新评分详情
            if (evaluationScore != null) {
                if (evaluationScore.getId() != null) {
                    // 更新现有评分
                    evaluationScoreMapper.updateById(evaluationScore);
                } else {
                    // 创建新评分
                    evaluationScore.setEvaluationId(evaluation.getId());
                    evaluationScoreMapper.insert(evaluationScore);
                }
            }

            return evaluationResult > 0;
        } catch (Exception e) {
            log.error("更新教师评价失败", e);
            throw new RuntimeException("更新教师评价失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Evaluation> getArtworkEvaluations(Long artworkId, Integer type) {
        LambdaQueryWrapper<Evaluation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Evaluation::getArtworkId, artworkId);
        
        if (type != null) {
            queryWrapper.eq(Evaluation::getType, type);
        }
        
        queryWrapper.orderByDesc(Evaluation::getCreatedAt);
        
        return evaluationMapper.selectList(queryWrapper);
    }

    @Override
    public Evaluation getEvaluationById(Long id) {
        return evaluationMapper.selectById(id);
    }

    @Override
    public EvaluationScore getEvaluationScore(Long evaluationId) {
        LambdaQueryWrapper<EvaluationScore> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationScore::getEvaluationId, evaluationId);
        return evaluationScoreMapper.selectOne(queryWrapper);
    }

    @Override
    public IPage<Evaluation> getUserEvaluations(Long userId, Page<Evaluation> page, Integer type) {
        LambdaQueryWrapper<Evaluation> queryWrapper = new LambdaQueryWrapper<>();

        // 通过作品ID关联查询用户的评价
        // 首先获取用户的所有作品ID
        List<Artwork> userArtworks = artworkService.getUserArtworks(userId, new Page<>(1, Integer.MAX_VALUE)).getRecords();
        if (userArtworks.isEmpty()) {
            // 如果用户没有作品，返回空结果
            return new Page<>(page.getCurrent(), page.getSize());
        }

        List<Long> artworkIds = userArtworks.stream()
                .map(Artwork::getId)
                .collect(java.util.stream.Collectors.toList());

        queryWrapper.in(Evaluation::getArtworkId, artworkIds);

        if (type != null) {
            queryWrapper.eq(Evaluation::getType, type);
        }

        queryWrapper.orderByDesc(Evaluation::getCreatedAt);

        IPage<Evaluation> evaluationPage = evaluationMapper.selectPage(page, queryWrapper);

        // 为每个评价添加作品信息
        Map<Long, Artwork> artworkMap = userArtworks.stream()
                .collect(java.util.stream.Collectors.toMap(Artwork::getId, artwork -> artwork));

        evaluationPage.getRecords().forEach(evaluation -> {
            Artwork artwork = artworkMap.get(evaluation.getArtworkId());
            if (artwork != null) {
                // 将作品信息设置到评价对象中
                evaluation.setArtworkTitle(artwork.getTitle());

                // 从images JSON中提取第一张图片作为封面
                String images = artwork.getImages();
                if (images != null && !images.trim().isEmpty()) {
                    try {
                        com.alibaba.fastjson.JSONArray imageArray = com.alibaba.fastjson.JSON.parseArray(images);
                        if (!imageArray.isEmpty()) {
                            evaluation.setArtworkCover(imageArray.getString(0));
                        }
                    } catch (Exception e) {
                        log.warn("解析作品图片失败: {}", e.getMessage());
                        evaluation.setArtworkCover(images); // 如果不是JSON格式，直接使用
                    }
                }
            }
        });

        return evaluationPage;
    }

    /**
     * 创建默认的AI评价结果（当AI服务失败时使用）
     */
    private JSONObject createDefaultAiResult() {
        JSONObject result = new JSONObject();

        // 默认评价内容
        result.put("technicalAnalysis", "这是一幅素描正方体作品。从整体来看，作品展现了基本的立体造型能力，透视关系基本正确，明暗对比较为明显。线条运用较为流畅，整体效果良好。");
        result.put("problemDiagnosis", "在透视准确性方面还有提升空间，建议加强对透视原理的理解和练习。比例关系需要更加精确，明暗层次可以更加丰富。");
        result.put("improvementPlan", "建议多练习基础的透视绘画，可以从简单的几何体开始，逐步提高对空间关系的把握。同时加强明暗对比的练习，学习光影的表现技法。");
        result.put("stageGoals", "短期目标：掌握基本透视原理，提高线条控制能力。中期目标：能够准确表现物体的立体感和空间关系。长期目标：形成个人的绘画风格，提高整体艺术表现力。");
        result.put("referenceMaterials", "推荐学习《素描基础教程》、《透视学原理》等经典教材，多观察优秀的素描作品，学习大师的表现技法。");

        // 默认评分
        Map<String, Integer> scores = new HashMap<>();
        scores.put("perspectiveScore", 16);
        scores.put("proportionScore", 17);
        scores.put("lightShadowScore", 15);
        scores.put("lineQualityScore", 18);
        scores.put("overallEffectScore", 16);
        result.put("scores", scores);

        // 计算总分
        int totalScore = scores.values().stream().mapToInt(Integer::intValue).sum();
        result.put("totalScore", totalScore);

        log.info("创建默认AI评价结果，总分: {}", totalScore);
        return result;
    }

    @Override
    public IPage<Evaluation> getTeacherEvaluations(Long evaluatorId, Page<Evaluation> page) {
        LambdaQueryWrapper<Evaluation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Evaluation::getType, 2) // 教师评价
                .eq(Evaluation::getEvaluatorId, evaluatorId)
                .orderByDesc(Evaluation::getCreatedAt);

        return evaluationMapper.selectPage(page, queryWrapper);
    }

    @Override
    public long count() {
        return evaluationMapper.selectCount(null);
    }

    @Override
    public IPage<Evaluation> getAllEvaluations(Page<Evaluation> page) {
        LambdaQueryWrapper<Evaluation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(Evaluation::getCreatedAt);
        return evaluationMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEvaluation(Long id) {
        try {
            // 删除评价记录（逻辑删除）
            evaluationMapper.deleteById(id);
            log.info("删除评价成功，评价ID: {}", id);
        } catch (Exception e) {
            log.error("删除评价失败，评价ID: {}", id, e);
            throw new RuntimeException("删除评价失败: " + e.getMessage());
        }
    }
}