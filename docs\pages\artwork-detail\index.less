@import '/variable.less';

.artwork-detail {
  background-color: @bg-color;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 作品图片区域 */
.artwork-images {
  background: white;
  margin-bottom: 20rpx;
}

.image-swiper {
  height: 600rpx;
}

.artwork-image {
  width: 100%;
  height: 100%;
}

/* 作品信息区域 */
.artwork-info {
  background: white;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  
  &.status-completed {
    color: #07c160;
  }
  
  &.status-pending {
    color: #ff9500;
  }
}

/* 评价区域 */
.evaluation-section {
  background: white;
  margin-bottom: 20rpx;
}

.section-title {
  padding: 32rpx 32rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.title-subtitle {
  font-size: 24rpx;
  color: #999;
}

/* 评分区域 */
.score-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.score-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}

.score-items {
  .score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12rpx 0;
  }
  
  .score-name {
    font-size: 26rpx;
    color: #666;
  }
  
  .score-value {
    font-size: 26rpx;
    color: #333;
    font-weight: 500;
  }
  
  .score-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 0;
    margin-top: 16rpx;
    border-top: 1rpx solid #f0f0f0;
    
    .total-name {
      font-size: 28rpx;
      color: #333;
      font-weight: 600;
    }
    
    .total-value {
      font-size: 28rpx;
      color: #ff6b35;
      font-weight: 600;
    }
  }
}

/* 评价项目 */
.evaluation-item {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.item-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 教师评价评论区 */
.teacher-evaluation-section {
  background: white;
  margin-bottom: 20rpx;

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 32rpx 16rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .title-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .evaluation-count {
      font-size: 24rpx;
      color: #999;
      margin-left: 8rpx;
    }

    .collapse-toggle {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      background: #f8f9fa;
      border-radius: 20rpx;

      .toggle-text {
        font-size: 24rpx;
        color: #666;
        margin-right: 8rpx;
      }

      .toggle-icon {
        font-size: 20rpx;
        color: #666;
        transition: transform 0.3s ease;

        &.collapsed {
          transform: rotate(-90deg);
        }

        &.expanded {
          transform: rotate(0deg);
        }
      }
    }
  }
}

.teacher-evaluations-container {
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.collapsed {
    max-height: 0;
  }

  &.expanded {
    max-height: 2000rpx;
  }
}

/* 添加评价区域 */
.add-evaluation-section {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.add-evaluation-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  background: #f8f9fa;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;

  .add-icon {
    font-size: 32rpx;
    color: #007aff;
    margin-right: 12rpx;
  }

  .add-text {
    font-size: 28rpx;
    color: #007aff;
  }
}

/* 评价列表 */
.evaluations-list {
  .evaluation-item {
    padding: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }
}

.evaluation-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;

  .teacher-avatar {
    width: 80rpx;
    height: 80rpx;
    margin-right: 20rpx;

    .avatar-img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: #f0f0f0;
    }
  }

  .teacher-info {
    flex: 1;

    .teacher-name {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 4rpx;
    }

    .teacher-title {
      font-size: 24rpx;
      color: #999;
    }
  }

  .evaluation-meta {
    text-align: right;

    .evaluation-time {
      display: block;
      font-size: 24rpx;
      color: #999;
      margin-bottom: 8rpx;
    }

    .evaluation-score {
      .score-text {
        font-size: 24rpx;
        color: #ff6b35;
        font-weight: 500;
        background: #fff3f0;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
      }
    }
  }
}

/* 评价内容 */
.evaluation-content {
  margin-bottom: 20rpx;

  .content-section {
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .content-label {
      font-size: 26rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
      display: block;
    }

    .content-text {
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      white-space: pre-wrap;
    }
  }
}

/* 评价操作 */
.evaluation-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;

  .action-btn {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    background: #f8f9fa;
    border-radius: 20rpx;

    .action-icon {
      font-size: 24rpx;
      margin-right: 8rpx;
    }

    .action-text {
      font-size: 24rpx;
      color: #666;
    }
  }
}

/* 无评价状态 */
.no-evaluations {
  padding: 80rpx 32rpx;
  text-align: center;

  .no-evaluations-text {
    display: block;
    font-size: 28rpx;
    color: #999;
    margin-bottom: 8rpx;
  }

  .no-evaluations-subtitle {
    font-size: 24rpx;
    color: #ccc;
  }
}

.no-evaluation {
  background: white;
  padding: 80rpx 32rpx;
  text-align: center;
}

.no-evaluation-text {
  font-size: 28rpx;
  color: #999;
}

/* 添加评价弹窗 */
.add-evaluation-modal {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  overflow: hidden;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .modal-close {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: #f0f0f0;

      .close-icon {
        font-size: 32rpx;
        color: #666;
      }
    }
  }

  .modal-content {
    padding: 32rpx;
    max-height: 60vh;
    overflow-y: auto;

    .score-section {
      margin-bottom: 32rpx;

      .section-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 16rpx;
      }

      .score-input {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .score-display {
          font-size: 28rpx;
          font-weight: 600;
          color: #ff6b35;
          min-width: 80rpx;
        }
      }
    }

    .input-section {
      margin-bottom: 32rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 16rpx;
      }
    }
  }

  .modal-footer {
    display: flex;
    gap: 20rpx;
    padding: 32rpx;
    border-top: 1rpx solid #f0f0f0;
  }
}
