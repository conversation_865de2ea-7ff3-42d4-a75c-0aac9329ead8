@import '/variable.less';

.my-teacher-evaluations {
  background-color: @bg-color;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 统计信息 */
.stats-header {
  display: flex;
  background: white;
  padding: 32rpx;
  margin-bottom: 20rpx;
  
  .stats-item {
    flex: 1;
    text-align: center;
    
    .stats-number {
      display: block;
      font-size: 48rpx;
      font-weight: 600;
      color: #ff6b35;
      margin-bottom: 8rpx;
    }
    
    .stats-label {
      font-size: 24rpx;
      color: #999;
    }
  }
}

/* 评价列表 */
.evaluation-list {
  .evaluation-item {
    background: white;
    margin-bottom: 20rpx;
    padding: 32rpx;
    
    .evaluation-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20rpx;
      
      .artwork-info {
        display: flex;
        align-items: flex-start;
        flex: 1;
        
        .artwork-thumb {
          width: 120rpx;
          height: 120rpx;
          border-radius: 12rpx;
          margin-right: 20rpx;
          background: #f0f0f0;
        }
        
        .artwork-details {
          flex: 1;
          
          .artwork-title {
            display: block;
            font-size: 28rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 8rpx;
            line-height: 1.4;
          }
          
          .evaluation-time {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
      
      .evaluation-score {
        .score-text {
          font-size: 24rpx;
          color: #ff6b35;
          font-weight: 500;
          background: #fff3f0;
          padding: 8rpx 16rpx;
          border-radius: 16rpx;
        }
      }
    }
    
    .evaluation-content {
      margin-bottom: 20rpx;
      
      .content-preview {
        margin-bottom: 12rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .content-label {
          font-size: 24rpx;
          color: #666;
          margin-right: 8rpx;
        }
        
        .content-text {
          font-size: 24rpx;
          color: #333;
          line-height: 1.5;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
      }
    }
    
    .evaluation-actions {
      display: flex;
      gap: 20rpx;
      
      .action-btn {
        display: flex;
        align-items: center;
        padding: 12rpx 20rpx;
        background: #f8f9fa;
        border-radius: 20rpx;
        
        .action-icon {
          font-size: 24rpx;
          margin-right: 8rpx;
        }
        
        .action-text {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  background: white;
  
  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 32rpx;
    opacity: 0.6;
  }
  
  .empty-text {
    font-size: 32rpx;
    color: #999;
    margin-bottom: 16rpx;
  }
  
  .empty-subtitle {
    font-size: 24rpx;
    color: #ccc;
  }
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  background: white;
  
  .loading-text {
    font-size: 24rpx;
    color: #999;
    margin-top: 16rpx;
  }
}
