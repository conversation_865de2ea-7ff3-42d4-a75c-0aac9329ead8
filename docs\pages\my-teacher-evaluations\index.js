import request from '~/api/request';
import { getCurrentUser, isTeacher } from '~/utils/auth';
import useToastBehavior from '~/behaviors/useToast';

Page({
  behaviors: [useToastBehavior],

  data: {
    evaluations: [],
    total: 0,
    thisMonth: 0,
    averageScore: 0,
    loading: true,
    page: 1,
    size: 10,
    hasMore: true
  },

  onLoad() {
    // 检查是否为教师
    if (!isTeacher()) {
      this.showToast('只有教师可以查看此页面');
      wx.navigateBack();
      return;
    }
    
    this.loadEvaluations();
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreEvaluations();
    }
  },

  onPullDownRefresh() {
    this.setData({
      page: 1,
      evaluations: [],
      hasMore: true
    });
    this.loadEvaluations();
  },

  async loadEvaluations() {
    try {
      this.setData({ loading: true });
      
      const currentUser = getCurrentUser();
      if (!currentUser) {
        this.showToast('请先登录');
        return;
      }

      const res = await request(`/evaluation/teacher/${currentUser.id}?page=${this.data.page}&size=${this.data.size}`, 'GET');
      
      if (res && res.code === 0) {
        const { records, total } = res.data;
        
        this.setData({
          evaluations: this.data.page === 1 ? records : [...this.data.evaluations, ...records],
          total,
          hasMore: records.length === this.data.size,
          loading: false
        });

        // 计算统计数据
        this.calculateStats(records);
        
        wx.stopPullDownRefresh();
      } else {
        this.showToast(res?.msg || '加载失败');
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error('加载教师评价失败:', error);
      this.showToast('加载失败，请重试');
      this.setData({ loading: false });
    }
  },

  async loadMoreEvaluations() {
    this.setData({
      page: this.data.page + 1
    });
    await this.loadEvaluations();
  },

  calculateStats(evaluations) {
    if (!evaluations || evaluations.length === 0) return;

    // 计算本月评价数
    const now = new Date();
    const thisMonth = evaluations.filter(evaluation => {
      const evalDate = new Date(evaluation.createdAt);
      return evalDate.getMonth() === now.getMonth() && evalDate.getFullYear() === now.getFullYear();
    }).length;

    // 计算平均评分
    const validScores = evaluations.filter(evaluation => evaluation.overallScore).map(evaluation => parseFloat(evaluation.overallScore));
    const averageScore = validScores.length > 0 ? Math.round(validScores.reduce((sum, score) => sum + score, 0) / validScores.length) : 0;

    this.setData({
      thisMonth,
      averageScore
    });
  },

  // 点击评价项
  onEvaluationTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/evaluation-detail/index?id=${id}`
    });
  },

  // 编辑评价
  editEvaluation(e) {
    const { id } = e.currentTarget.dataset;
    // TODO: 实现编辑功能
    this.showToast('编辑功能开发中');
  },

  // 查看作品
  viewArtwork(e) {
    const { artworkId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/artwork-detail/index?id=${artworkId}`
    });
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '';
    
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;
    
    // 小于1分钟
    if (diff < 60000) {
      return '刚刚';
    }
    
    // 小于1小时
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }
    
    // 小于1天
    if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    }
    
    // 小于7天
    if (diff < 604800000) {
      return Math.floor(diff / 86400000) + '天前';
    }
    
    // 超过7天显示具体日期
    return time.toLocaleDateString();
  }
});
