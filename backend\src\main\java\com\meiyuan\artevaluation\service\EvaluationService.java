package com.meiyuan.artevaluation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.Evaluation;
import com.meiyuan.artevaluation.entity.EvaluationScore;

import java.util.List;

/**
 * 评价服务接口
 */
public interface EvaluationService {

    /**
     * 创建AI评价
     *
     * @param artworkId 作品ID
     * @param imageUrl 图片URL
     * @return 评价ID
     */
    Long createAiEvaluation(Long artworkId, String imageUrl);

    /**
     * 创建教师评价
     *
     * @param evaluation 评价信息
     * @param evaluationScore 评分详情
     * @return 评价ID
     */
    Long createTeacherEvaluation(Evaluation evaluation, EvaluationScore evaluationScore);

    /**
     * 更新教师评价
     *
     * @param evaluation 评价信息
     * @param evaluationScore 评分详情
     * @return 是否成功
     */
    boolean updateTeacherEvaluation(Evaluation evaluation, EvaluationScore evaluationScore);

    /**
     * 获取作品评价
     *
     * @param artworkId 作品ID
     * @param type 评价类型：1-AI评价，2-教师评价，null-所有
     * @return 评价列表
     */
    List<Evaluation> getArtworkEvaluations(Long artworkId, Integer type);

    /**
     * 获取评价详情
     *
     * @param id 评价ID
     * @return 评价信息
     */
    Evaluation getEvaluationById(Long id);

    /**
     * 获取评分详情
     *
     * @param evaluationId 评价ID
     * @return 评分详情
     */
    EvaluationScore getEvaluationScore(Long evaluationId);

    /**
     * 分页查询用户评价
     *
     * @param userId 用户ID
     * @param page 分页参数
     * @param type 评价类型：1-AI评价，2-教师评价，null-所有
     * @return 评价列表
     */
    IPage<Evaluation> getUserEvaluations(Long userId, Page<Evaluation> page, Integer type);

    /**
     * 分页查询教师评价
     *
     * @param evaluatorId 教师ID
     * @param page 分页参数
     * @return 评价列表
     */
    IPage<Evaluation> getTeacherEvaluations(Long evaluatorId, Page<Evaluation> page);

    /**
     * 获取评价总数
     *
     * @return 评价总数
     */
    long count();

    /**
     * 分页获取所有评价
     *
     * @param page 分页参数
     * @return 评价列表
     */
    IPage<Evaluation> getAllEvaluations(Page<Evaluation> page);

    /**
     * 删除评价
     *
     * @param id 评价ID
     */
    void deleteEvaluation(Long id);
}