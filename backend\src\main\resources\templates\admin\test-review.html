<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试审核功能</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>测试审核功能</h2>
        
        <div class="card">
            <div class="card-body">
                <h5>测试按钮点击</h5>
                <button class="btn btn-success" onclick="testReview(1, 1)">
                    测试通过审核
                </button>
                <button class="btn btn-danger" onclick="testReview(1, 2)">
                    测试拒绝审核
                </button>
                
                <div id="result" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script>
        function testReview(applicationId, status) {
            console.log('testReview called with:', applicationId, status);
            
            const statusText = status === 1 ? '通过' : '拒绝';
            const remark = prompt(`请输入${statusText}理由（可选）:`);
            
            if (remark === null) {
                console.log('用户取消了操作');
                return;
            }
            
            if (confirm(`确定要${statusText}该申请吗？`)) {
                console.log('开始发送请求...');
                
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<div class="alert alert-info">正在处理...</div>';
                
                fetch(`/admin/teacher-applications/${applicationId}/review`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        status: status,
                        adminRemark: remark || ''
                    })
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.code === 0) {
                        resultDiv.innerHTML = `<div class="alert alert-success">${data.msg}</div>`;
                    } else {
                        resultDiv.innerHTML = `<div class="alert alert-danger">操作失败: ${data.msg}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    resultDiv.innerHTML = `<div class="alert alert-danger">操作失败: ${error.message}</div>`;
                });
            } else {
                console.log('用户取消了确认');
            }
        }
    </script>
</body>
</html>
