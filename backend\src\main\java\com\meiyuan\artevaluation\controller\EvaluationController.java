package com.meiyuan.artevaluation.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meiyuan.artevaluation.entity.Evaluation;
import com.meiyuan.artevaluation.entity.EvaluationScore;
import com.meiyuan.artevaluation.service.EvaluationService;
import com.meiyuan.artevaluation.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评价控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/evaluation")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class EvaluationController {

    private final EvaluationService evaluationService;
    private final UserService userService;

    /**
     * 获取评价详情
     *
     * @param id 评价ID
     * @return 评价详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getEvaluationDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Evaluation evaluation = evaluationService.getEvaluationById(id);
            if (evaluation == null) {
                result.put("code", 1);
                result.put("msg", "评价不存在");
                return result;
            }
            
            // 获取评分详情
            EvaluationScore score = evaluationService.getEvaluationScore(id);
            
            Map<String, Object> data = new HashMap<>();
            data.put("evaluation", evaluation);
            data.put("score", score);
            
            result.put("code", 0);
            result.put("msg", "获取成功");
            result.put("data", data);
        } catch (Exception e) {
            log.error("获取评价详情异常", e);
            result.put("code", 1);
            result.put("msg", "获取失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取作品评价列表
     *
     * @param artworkId 作品ID
     * @param type 评价类型：1-AI评价，2-教师评价
     * @return 评价列表
     */
    @GetMapping("/artwork/{artworkId}")
    public Map<String, Object> getArtworkEvaluations(
            @PathVariable Long artworkId,
            @RequestParam(required = false) Integer type) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Evaluation> evaluations = evaluationService.getArtworkEvaluations(artworkId, type);
            
            result.put("code", 0);
            result.put("msg", "获取成功");
            result.put("data", evaluations);
        } catch (Exception e) {
            log.error("获取作品评价列表异常", e);
            result.put("code", 1);
            result.put("msg", "获取失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 创建教师评价
     *
     * @param evaluation 评价信息
     * @param score 评分详情
     * @return 创建结果
     */
    @PostMapping("/teacher")
    public Map<String, Object> createTeacherEvaluation(
            @RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();

        try {
            Long artworkId = Long.valueOf(params.get("artworkId").toString());
            Long evaluatorId = Long.valueOf(params.get("evaluatorId").toString());

            // 验证评价者是否为教师
            Integer userRole = userService.getUserRole(evaluatorId);
            if (userRole == null || userRole != 2) {
                result.put("code", 1);
                result.put("msg", "只有教师可以进行评价");
                return result;
            }

            // 检查该教师是否已经评价过此作品
            List<Evaluation> existingEvaluations = evaluationService.getArtworkEvaluations(artworkId, 2);
            boolean hasEvaluated = existingEvaluations.stream()
                    .anyMatch(eval -> eval.getEvaluatorId() != null && eval.getEvaluatorId().equals(evaluatorId));

            if (hasEvaluated) {
                result.put("code", 1);
                result.put("msg", "您已经评价过此作品，不能重复评价");
                return result;
            }

            // 解析参数
            Evaluation evaluation = new Evaluation();
            evaluation.setArtworkId(artworkId);
            evaluation.setEvaluatorId(evaluatorId);
            evaluation.setTechnicalAnalysis((String) params.get("technicalAnalysis"));
            evaluation.setProblemDiagnosis((String) params.get("problemDiagnosis"));
            evaluation.setImprovementPlan((String) params.get("improvementPlan"));
            evaluation.setStageGoals((String) params.get("stageGoals"));
            evaluation.setReferenceMaterials((String) params.get("referenceMaterials"));
            evaluation.setOverallScore(new java.math.BigDecimal(params.get("overallScore").toString()));

            // 评分详情（可选）
            EvaluationScore evaluationScore = null;
            Map<String, Object> scoreMap = (Map<String, Object>) params.get("score");
            if (scoreMap != null) {
                evaluationScore = new EvaluationScore();
                evaluationScore.setPerspectiveScore(new java.math.BigDecimal(scoreMap.get("perspectiveScore").toString()));
                evaluationScore.setProportionScore(new java.math.BigDecimal(scoreMap.get("proportionScore").toString()));
                evaluationScore.setLightShadowScore(new java.math.BigDecimal(scoreMap.get("lightShadowScore").toString()));
                evaluationScore.setLineQualityScore(new java.math.BigDecimal(scoreMap.get("lineQualityScore").toString()));
                evaluationScore.setOverallEffectScore(new java.math.BigDecimal(scoreMap.get("overallEffectScore").toString()));
            }

            // 创建评价
            Long evaluationId = evaluationService.createTeacherEvaluation(evaluation, evaluationScore);

            result.put("code", 0);
            result.put("msg", "评价成功");
            result.put("data", evaluationId);
        } catch (Exception e) {
            log.error("创建教师评价异常", e);
            result.put("code", 1);
            result.put("msg", "评价失败: " + e.getMessage());
        }

        return result;
    }
    
    /**
     * 获取用户评价列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param type 评价类型：1-AI评价，2-教师评价，null-所有
     * @return 评价列表
     */
    @GetMapping("/user/{userId}")
    public Map<String, Object> getUserEvaluations(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer type) {
        Map<String, Object> result = new HashMap<>();

        try {
            Page<Evaluation> pageParam = new Page<>(page, size);
            IPage<Evaluation> evaluations = evaluationService.getUserEvaluations(userId, pageParam, type);

            result.put("code", 0);
            result.put("msg", "获取成功");
            result.put("data", evaluations);
        } catch (Exception e) {
            log.error("获取用户评价列表异常", e);
            result.put("code", 1);
            result.put("msg", "获取失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取教师评价列表
     *
     * @param evaluatorId 教师ID
     * @param page 页码
     * @param size 每页大小
     * @return 评价列表
     */
    @GetMapping("/teacher/{evaluatorId}")
    public Map<String, Object> getTeacherEvaluations(
            @PathVariable Long evaluatorId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        Map<String, Object> result = new HashMap<>();

        try {
            Page<Evaluation> pageParam = new Page<>(page, size);
            IPage<Evaluation> evaluations = evaluationService.getTeacherEvaluations(evaluatorId, pageParam);

            result.put("code", 0);
            result.put("msg", "获取成功");
            result.put("data", evaluations);
        } catch (Exception e) {
            log.error("获取教师评价列表异常", e);
            result.put("code", 1);
            result.put("msg", "获取失败: " + e.getMessage());
        }

        return result;
    }
} 