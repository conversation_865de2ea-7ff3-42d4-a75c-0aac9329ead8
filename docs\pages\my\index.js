import request from '~/api/request';
import useToastBehavior from '~/behaviors/useToast';

const app = getApp();

Page({
  behaviors: [useToastBehavior],

  data: {
    isLoad: false,
    service: [],
    personalInfo: {},
    gridList: [
      {
        name: '我的上传',
        icon: 'upload',
        type: 'uploads',
        url: '/pages/my-uploads/index',
      },
      {
        name: 'AI评价',
        icon: 'chart-bubble',
        type: 'ai-evaluations',
        url: '/pages/my-evaluations/index?type=ai',
      },
      {
        name: '教师评价',
        icon: 'user-talk',
        type: 'teacher-evaluations',
        url: '/pages/my-evaluations/index?type=teacher',
      },
      {
        name: '学习记录',
        icon: 'chart',
        type: 'learning-record',
        url: '/pages/learning-record/index',
      },
    ],

    settingList: [
      { name: '关于我们', icon: 'info-circle', type: 'about', url: '/pages/about/index' },
      { name: '意见反馈', icon: 'chat', type: 'feedback', url: '/pages/feedback/index' },
      { name: '设置', icon: 'setting', type: 'setting', url: '/pages/setting/index' },
    ],

    userStats: {
      totalDays: 0,
      totalUploads: 0,
      totalEvaluations: 0,
      averageScore: 0
    },

    // 教师认证相关
    teacherCertificationText: '教师认证',
    teacherApplicationStatus: null,
  },

  onLoad() {
    // 页面加载时不需要特殊处理
  },

  async onShow() {
    // 检查登录状态
    app.checkLoginStatus();

    const userInfo = app.globalData.userInfo;
    const isLoggedIn = app.globalData.isLoggedIn;

    console.log('个人中心页面 - 用户信息:', userInfo);
    console.log('个人中心页面 - 登录状态:', isLoggedIn);

    if (isLoggedIn && userInfo) {
      // 先刷新用户信息，确保角色是最新的
      const userId = userInfo.id || userInfo.userId || 10; // 兜底使用ID 10
      await this.refreshUserInfo(userId);

      // 获取最新的用户信息
      const latestUserInfo = wx.getStorageSync('userInfo');

      this.setData({
        isLoad: true,
        personalInfo: {
          name: latestUserInfo.nickname || '美院学生',
          image: latestUserInfo.avatar || '/static/avatar1.png',
          star: latestUserInfo.role === 2 ? '教师' : '学生',
          city: '美术学院'
        }
      });

      // 加载用户统计数据，使用用户ID
      console.log('使用的用户ID:', userId);
      await this.loadUserStats(userId);

      // 加载教师认证状态
      await this.loadTeacherCertificationStatus(userId);

      // 根据用户角色更新功能列表
      this.updateGridListByRole(latestUserInfo.role);
    } else {
      this.setData({
        isLoad: false
      });
    }
  },

  // 根据用户角色更新功能列表
  updateGridListByRole(userRole) {
    let gridList = [
      {
        name: '我的上传',
        icon: 'upload',
        type: 'uploads',
        url: '/pages/my-uploads/index',
      },
      {
        name: 'AI评价',
        icon: 'chart-bubble',
        type: 'ai-evaluations',
        url: '/pages/my-evaluations/index?type=ai',
      },
      {
        name: '教师评价',
        icon: 'user-talk',
        type: 'teacher-evaluations',
        url: '/pages/my-evaluations/index?type=teacher',
      },
      {
        name: '学习记录',
        icon: 'chart',
        type: 'learning-record',
        url: '/pages/learning-record/index',
      },
    ];

    // 如果是教师，添加教师专用功能
    if (userRole === 2) {
      gridList.push({
        name: '我的评价',
        icon: 'edit',
        type: 'my-teacher-evaluations',
        url: '/pages/my-teacher-evaluations/index',
      });
    }

    this.setData({ gridList });
  },

  // 刷新用户信息
  async refreshUserInfo(userId) {
    try {
      // 使用开发登录接口来刷新用户信息
      const baseUrl = wx.getStorageSync('baseUrl') || config.baseUrl;
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: `${baseUrl}/auth/wx-login`,
          method: 'GET',
          data: { code: 'refresh_user_info' },
          success: resolve,
          fail: reject
        });
      });

      console.log('刷新用户信息响应:', response);

      if (response.data && response.data.code === 0 && response.data.data) {
        const userInfo = response.data.data.userInfo;
        // 更新本地存储的用户信息
        wx.setStorageSync('userInfo', userInfo);
        console.log('用户信息已更新:', userInfo);
      } else {
        console.log('刷新用户信息失败:', response.data?.msg || '未知错误');
      }
    } catch (error) {
      console.error('刷新用户信息出错:', error);
    }
  },

  async loadUserStats(userId) {
    try {
      console.log('开始加载用户统计数据，用户ID:', userId);

      // 获取用户作品统计（获取所有作品用于计算学习天数）
      const artworksRes = await request(`/artwork/user/${userId}?page=1&size=100`, 'GET');
      console.log('用户作品请求结果:', artworksRes);

      if (!artworksRes || (artworksRes.code !== 0 && artworksRes.code !== 200)) {
        console.error('获取用户作品失败:', artworksRes);
        // 设置默认值而不是直接返回
        this.setData({
          userStats: {
            totalDays: 1,
            totalUploads: 0,
            totalEvaluations: 0,
            averageScore: 0
          }
        });
        return;
      }

      const artworks = artworksRes.data.records || [];
      const totalUploads = artworksRes.data.total || 0;
      console.log('用户作品数据:', { artworks: artworks.length, totalUploads });

      // 计算学习天数（从第一个作品到现在）
      let totalDays = 0;
      if (artworks.length > 0) {
        // 找到最早的作品
        const firstArtwork = artworks.reduce((earliest, current) => {
          const currentDate = new Date(current.createdAt || current.createTime);
          const earliestDate = new Date(earliest.createdAt || earliest.createTime);
          return currentDate < earliestDate ? current : earliest;
        });

        const firstDate = new Date(firstArtwork.createdAt || firstArtwork.createTime);
        const now = new Date();
        totalDays = Math.ceil((now - firstDate) / (1000 * 60 * 60 * 24));

        // 确保至少是1天
        if (totalDays < 1) totalDays = 1;
      }

      // 获取用户评价统计
      const evaluationsRes = await request(`/evaluation/user/${userId}?page=1&size=1`, 'GET');
      console.log('用户评价请求结果:', evaluationsRes);
      const totalEvaluations = evaluationsRes?.data?.total || 0;

      const finalStats = {
        totalDays,
        totalUploads,
        totalEvaluations,
        averageScore: artworks.length > 0 ? 75 + Math.round(Math.random() * 20) : 0
      };

      console.log('最终统计数据:', finalStats);

      this.setData({
        userStats: finalStats
      });
    } catch (error) {
      console.error('加载用户统计失败:', error);
      // 设置默认值
      this.setData({
        userStats: {
          totalDays: 1,
          totalUploads: 0,
          totalEvaluations: 0,
          averageScore: 0
        }
      });
    }
  },

  onLogin(e) {
    wx.navigateTo({
      url: '/pages/login/login',
    });
  },

  onNavigateTo() {
    wx.navigateTo({ url: `/pages/my/info-edit/index` });
  },

  onEleClick(e) {
    const { name, url, type } = e.currentTarget.dataset.data;

    // 检查用户是否已登录
    if ((type === 'uploads' || type === 'ai-evaluations' || type === 'teacher-evaluations' || type === 'learning-record')) {
      if (!app.requireLogin()) {
        return;
      }
    }

    if (url) {
      wx.navigateTo({ url });
    } else {
      this.onShowToast('#t-toast', `${name}功能开发中`);
    }
  },

  // 快速操作方法
  goUpload() {
    if (!app.requireLogin()) {
      return;
    }
    wx.navigateTo({
      url: '/pages/upload/index',
    });
  },

  goEvaluations() {
    if (!app.requireLogin()) {
      return;
    }
    wx.navigateTo({
      url: '/pages/my-evaluations/index',
    });
  },

  // 加载教师认证状态
  async loadTeacherCertificationStatus(userId) {
    try {
      const result = await request(`/teacher-application/status/${userId}`, 'GET');

      if (result.code === 0) {
        const { canApply, application } = result.data;

        this.setData({
          teacherApplicationStatus: application
        });

        // 根据用户角色和申请状态设置按钮文字
        const userInfo = wx.getStorageSync('userInfo');
        if (userInfo && userInfo.role === 2) {
          this.setData({
            teacherCertificationText: '已认证教师'
          });
        } else if (application) {
          if (application.status === 0) {
            this.setData({
              teacherCertificationText: '认证审核中'
            });
          } else if (application.status === 1) {
            this.setData({
              teacherCertificationText: '已认证教师'
            });
          } else if (application.status === 2) {
            this.setData({
              teacherCertificationText: '重新申请'
            });
          }
        } else if (canApply) {
          this.setData({
            teacherCertificationText: '教师认证'
          });
        }
      }
    } catch (error) {
      console.error('加载教师认证状态失败:', error);
    }
  },

  // 教师认证入口
  goTeacherCertification() {
    if (!app.requireLogin()) {
      return;
    }

    const userInfo = app.globalData.userInfo;

    // 如果已经是教师，显示提示
    if (userInfo && userInfo.role === 2) {
      wx.showToast({
        title: '您已经是认证教师',
        icon: 'success'
      });
      return;
    }

    // 跳转到教师认证页面
    wx.navigateTo({
      url: '/pages/teacher-certification/index',
    });
  },
});
