<t-toast id="t-toast" />

<view class="my-teacher-evaluations">
  <nav titleText="我的评价" navType="title" />
  
  <!-- 统计信息 -->
  <view class="stats-header">
    <view class="stats-item">
      <text class="stats-number">{{total}}</text>
      <text class="stats-label">总评价数</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{thisMonth}}</text>
      <text class="stats-label">本月评价</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{averageScore}}</text>
      <text class="stats-label">平均评分</text>
    </view>
  </view>

  <!-- 评价列表 -->
  <view class="evaluation-list" wx:if="{{evaluations.length > 0}}">
    <view 
      class="evaluation-item" 
      wx:for="{{evaluations}}" 
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="onEvaluationTap"
    >
      <view class="evaluation-header">
        <view class="artwork-info">
          <image class="artwork-thumb" src="{{item.artworkCover}}" mode="aspectFill" />
          <view class="artwork-details">
            <text class="artwork-title">{{item.artworkTitle}}</text>
            <text class="evaluation-time">{{formatTime(item.createdAt)}}</text>
          </view>
        </view>
        <view class="evaluation-score" wx:if="{{item.overallScore}}">
          <text class="score-text">{{item.overallScore}}分</text>
        </view>
      </view>

      <view class="evaluation-content">
        <view wx:if="{{item.technicalAnalysis}}" class="content-preview">
          <text class="content-label">技术分析：</text>
          <text class="content-text">{{item.technicalAnalysis}}</text>
        </view>
        <view wx:if="{{item.problemDiagnosis}}" class="content-preview">
          <text class="content-label">问题诊断：</text>
          <text class="content-text">{{item.problemDiagnosis}}</text>
        </view>
      </view>
      
      <view class="evaluation-actions">
        <view class="action-btn" bindtap="editEvaluation" data-id="{{item.id}}" catchtap="true">
          <text class="action-icon">✏️</text>
          <text class="action-text">编辑</text>
        </view>
        <view class="action-btn" bindtap="viewArtwork" data-artwork-id="{{item.artworkId}}" catchtap="true">
          <text class="action-icon">👁️</text>
          <text class="action-text">查看作品</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{evaluations.length === 0 && !loading}}" class="empty-state">
    <image class="empty-icon" src="/static/empty-evaluation.png" />
    <text class="empty-text">暂无评价记录</text>
    <text class="empty-subtitle">去为学生作品提供专业指导吧</text>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-state">
    <t-loading theme="circular" size="48rpx" />
    <text class="loading-text">加载中...</text>
  </view>
</view>
