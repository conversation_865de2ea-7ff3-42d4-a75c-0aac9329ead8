<t-navbar title="作品详情" left-arrow />

<view class="artwork-detail">
  <!-- 作品图片 -->
  <view class="artwork-images">
    <swiper class="image-swiper" indicator-dots="{{images.length > 1}}" autoplay="{{false}}">
      <swiper-item wx:for="{{images}}" wx:key="index">
        <image src="{{item}}" class="artwork-image" mode="aspectFit" bindtap="previewImage" data-url="{{item}}" />
      </swiper-item>
    </swiper>
  </view>

  <!-- 作品信息 -->
  <view class="artwork-info">
    <view class="info-item">
      <text class="info-label">作品标题</text>
      <text class="info-value">{{artwork.title}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">上传时间</text>
      <text class="info-value">{{artwork.createdAt}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">评价状态</text>
      <text class="info-value {{artwork.status === 2 ? 'status-completed' : 'status-pending'}}">
        {{artwork.status === 2 ? '已评价' : '评价中'}}
      </text>
    </view>
  </view>

  <!-- AI评价结果 -->
  <view wx:if="{{aiEvaluation}}" class="evaluation-section">
    <view class="section-title">
      <text class="title-text">AI专业评价</text>
      <text class="title-subtitle">基于美院评价标准</text>
    </view>

    <!-- 量化评分 -->
    <view wx:if="{{evaluationScore}}" class="score-section">
      <view class="score-title">量化评分系统（总分100分）</view>
      <view class="score-items">
        <view class="score-item">
          <text class="score-name">透视准确性</text>
          <text class="score-value">{{evaluationScore.perspectiveScore}}/20分</text>
        </view>
        <view class="score-item">
          <text class="score-name">比例结构</text>
          <text class="score-value">{{evaluationScore.proportionScore}}/20分</text>
        </view>
        <view class="score-item">
          <text class="score-name">明暗关系</text>
          <text class="score-value">{{evaluationScore.lightShadowScore}}/20分</text>
        </view>
        <view class="score-item">
          <text class="score-name">线条质量</text>
          <text class="score-value">{{evaluationScore.lineQualityScore}}/20分</text>
        </view>
        <view class="score-item">
          <text class="score-name">整体效果</text>
          <text class="score-value">{{evaluationScore.overallEffectScore}}/20分</text>
        </view>
        <view class="score-total">
          <text class="total-name">总分</text>
          <text class="total-value">{{aiEvaluation.overallScore}}/100分</text>
        </view>
      </view>
    </view>

    <!-- 技术亮点评估 -->
    <view wx:if="{{aiEvaluation.technicalAnalysis}}" class="evaluation-item">
      <view class="item-title">1. 技术亮点评估</view>
      <view class="item-content">{{aiEvaluation.technicalAnalysis}}</view>
    </view>

    <!-- 核心问题诊断 -->
    <view wx:if="{{aiEvaluation.problemDiagnosis}}" class="evaluation-item">
      <view class="item-title">2. 核心问题诊断</view>
      <view class="item-content">{{aiEvaluation.problemDiagnosis}}</view>
    </view>

    <!-- 专项改进方案 -->
    <view wx:if="{{aiEvaluation.improvementPlan}}" class="evaluation-item">
      <view class="item-title">3. 专项改进方案</view>
      <view class="item-content">{{aiEvaluation.improvementPlan}}</view>
    </view>

    <!-- 阶段提升目标 -->
    <view wx:if="{{aiEvaluation.stageGoals}}" class="evaluation-item">
      <view class="item-title">4. 阶段提升目标</view>
      <view class="item-content">{{aiEvaluation.stageGoals}}</view>
    </view>

    <!-- 参考资料 -->
    <view wx:if="{{aiEvaluation.referenceMaterials}}" class="evaluation-item">
      <view class="item-title">5. 参考资料推荐</view>
      <view class="item-content">{{aiEvaluation.referenceMaterials}}</view>
    </view>
  </view>

  <!-- 教师评价评论区 -->
  <view class="teacher-evaluation-section">
    <view class="section-title">
      <text class="title-text">教师评价</text>
      <text class="evaluation-count">({{teacherEvaluations.length}})</text>
      <view class="collapse-toggle" bindtap="toggleTeacherEvaluations">
        <text class="toggle-text">{{teacherEvaluationsCollapsed ? '展开' : '收起'}}</text>
        <text class="toggle-icon {{teacherEvaluationsCollapsed ? 'collapsed' : 'expanded'}}">▼</text>
      </view>
    </view>

    <!-- 教师评价列表 -->
    <view class="teacher-evaluations-container {{teacherEvaluationsCollapsed ? 'collapsed' : 'expanded'}}">
      <!-- 添加评价按钮 (仅教师可见) -->
      <view wx:if="{{isTeacher && !hasUserEvaluated}}" class="add-evaluation-section">
        <view class="add-evaluation-btn" bindtap="showAddEvaluationModal">
          <text class="add-icon">+</text>
          <text class="add-text">添加我的评价</text>
        </view>
      </view>

      <!-- 评价列表 -->
      <view wx:if="{{teacherEvaluations.length > 0}}" class="evaluations-list">
        <view wx:for="{{teacherEvaluations}}" wx:key="id" class="evaluation-item">
          <view class="evaluation-header">
            <view class="teacher-avatar">
              <image src="{{item.teacherAvatar || '/static/default-teacher-avatar.png'}}" class="avatar-img" />
            </view>
            <view class="teacher-info">
              <text class="teacher-name">{{item.teacherName || '匿名教师'}}</text>
              <text class="teacher-title">{{item.teacherTitle || '专业教师'}}</text>
            </view>
            <view class="evaluation-meta">
              <text class="evaluation-time">{{formatTime(item.createdAt)}}</text>
              <view wx:if="{{item.overallScore}}" class="evaluation-score">
                <text class="score-text">{{item.overallScore}}分</text>
              </view>
            </view>
          </view>

          <view class="evaluation-content">
            <view wx:if="{{item.technicalAnalysis}}" class="content-section">
              <text class="content-label">技术分析：</text>
              <text class="content-text">{{item.technicalAnalysis}}</text>
            </view>
            <view wx:if="{{item.problemDiagnosis}}" class="content-section">
              <text class="content-label">问题诊断：</text>
              <text class="content-text">{{item.problemDiagnosis}}</text>
            </view>
            <view wx:if="{{item.improvementPlan}}" class="content-section">
              <text class="content-label">改进建议：</text>
              <text class="content-text">{{item.improvementPlan}}</text>
            </view>
          </view>

          <!-- 评价操作 -->
          <view class="evaluation-actions">
            <view class="action-btn" bindtap="likeEvaluation" data-id="{{item.id}}">
              <text class="action-icon">👍</text>
              <text class="action-text">有用 {{item.likeCount || 0}}</text>
            </view>
            <view wx:if="{{item.evaluatorId === currentUserId}}" class="action-btn" bindtap="editEvaluation" data-id="{{item.id}}">
              <text class="action-icon">✏️</text>
              <text class="action-text">编辑</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 无评价提示 -->
      <view wx:if="{{teacherEvaluations.length === 0}}" class="no-evaluations">
        <text class="no-evaluations-text">暂无教师评价</text>
        <text class="no-evaluations-subtitle">等待专业教师为作品提供指导意见</text>
      </view>
    </view>
  </view>

  <!-- 无评价状态 -->
  <view wx:if="{{!aiEvaluation && teacherEvaluations.length === 0}}" class="no-evaluation">
    <text class="no-evaluation-text">AI正在分析中，请稍后查看...</text>
  </view>
</view>

<!-- 添加教师评价弹窗 -->
<t-popup visible="{{showAddEvaluationModal}}" placement="bottom" bind:visible-change="onAddEvaluationModalChange">
  <view class="add-evaluation-modal">
    <view class="modal-header">
      <text class="modal-title">添加教师评价</text>
      <view class="modal-close" bindtap="hideAddEvaluationModal">
        <text class="close-icon">×</text>
      </view>
    </view>

    <view class="modal-content">
      <!-- 评分区域 -->
      <view class="score-section">
        <text class="section-title">综合评分</text>
        <view class="score-input">
          <t-slider value="{{newEvaluation.overallScore}}" min="0" max="100" step="1"
                   bind:change="onScoreChange" show-value />
          <text class="score-display">{{newEvaluation.overallScore}}分</text>
        </view>
      </view>

      <!-- 技术分析 -->
      <view class="input-section">
        <text class="section-title">技术分析</text>
        <t-textarea placeholder="请分析作品的技术亮点和表现..."
                   value="{{newEvaluation.technicalAnalysis}}"
                   bind:change="onTechnicalAnalysisChange"
                   maxlength="500" />
      </view>

      <!-- 问题诊断 -->
      <view class="input-section">
        <text class="section-title">问题诊断</text>
        <t-textarea placeholder="请指出作品存在的问题..."
                   value="{{newEvaluation.problemDiagnosis}}"
                   bind:change="onProblemDiagnosisChange"
                   maxlength="500" />
      </view>

      <!-- 改进建议 -->
      <view class="input-section">
        <text class="section-title">改进建议</text>
        <t-textarea placeholder="请提供具体的改进建议..."
                   value="{{newEvaluation.improvementPlan}}"
                   bind:change="onImprovementPlanChange"
                   maxlength="500" />
      </view>
    </view>

    <view class="modal-footer">
      <t-button theme="light" size="large" bind:tap="hideAddEvaluationModal">取消</t-button>
      <t-button theme="primary" size="large" bind:tap="submitEvaluation" loading="{{submitting}}">
        提交评价
      </t-button>
    </view>
  </view>
</t-popup>

<t-message id="t-message" />
