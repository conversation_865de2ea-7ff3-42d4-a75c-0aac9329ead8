package com.meiyuan.artevaluation.controller;

import com.alibaba.fastjson.JSONObject;
import com.meiyuan.artevaluation.entity.User;
import com.meiyuan.artevaluation.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class AuthController {

    private final UserService userService;
    
    @Value("${wechat.miniapp.app-id}")
    private String appId;
    
    @Value("${wechat.miniapp.app-secret}")
    private String appSecret;
    
    // 添加开发环境标志，默认为true表示开发环境
    @Value("${wechat.miniapp.dev-mode:true}")
    private boolean devMode;
    
    private final OkHttpClient client = new OkHttpClient();

    /**
     * 微信登录
     *
     * @param code 微信授权码
     * @return 登录结果
     */
    @GetMapping("/wx-login")
    public Map<String, Object> wxLogin(@RequestParam String code) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 开发环境下，使用模拟登录
            if (devMode) {
                log.info("开发环境，使用模拟登录，code: {}", code);

                // 开发环境使用固定的openid，模拟同一个用户多次登录
                String openid = "dev_default_user"; // 固定openid，确保同一用户

                // 查询或创建用户
                User user = userService.getUserByOpenid(openid);
                if (user == null) {
                    user = new User();
                    user.setOpenid(openid);
                    user.setNickname("开发测试用户");
                    user.setAvatar("/static/default-avatar.svg");
                    user.setRole(1); // 默认为学生角色
                    user = userService.createOrUpdateUser(user);
                    log.info("开发环境：自动注册新用户: openid={}, userId={}", openid, user.getId());
                } else {
                    log.info("开发环境：用户已存在，直接登录: openid={}, userId={}", openid, user.getId());
                }
                
                // 返回用户信息和token
                Map<String, Object> data = new HashMap<>();
                data.put("userInfo", user);
                data.put("token", openid); // 简化处理，使用openid作为token

                result.put("code", 0);
                result.put("msg", "登录成功");
                result.put("data", data);
                
                return result;
            }
            
            // 生产环境，调用微信接口
            String url = String.format(
                    "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                    appId, appSecret, code);
            
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                JSONObject wxResult = JSONObject.parseObject(responseBody);
                
                log.info("微信登录结果: {}", wxResult);
                
                if (wxResult.containsKey("openid")) {
                    String openid = wxResult.getString("openid");
                    
                    // 查询或创建用户
                    User user = userService.getUserByOpenid(openid);
                    if (user == null) {
                        user = new User();
                        user.setOpenid(openid);
                        user.setNickname("美院学生" + (System.currentTimeMillis() % 10000));
                        user.setAvatar("/static/default-avatar.svg");
                        user.setRole(1); // 默认为学生角色
                        user = userService.createOrUpdateUser(user);
                        log.info("生产环境：自动注册新用户: openid={}, userId={}", openid, user.getId());
                    } else {
                        log.info("生产环境：用户已存在，直接登录: openid={}, userId={}", openid, user.getId());
                    }
                    
                    // 返回用户信息和token
                    Map<String, Object> data = new HashMap<>();
                    data.put("userInfo", user);
                    data.put("token", openid); // 简化处理，使用openid作为token

                    result.put("code", 0);
                    result.put("msg", "登录成功");
                    result.put("data", data);
                } else {
                    result.put("code", 1);
                    result.put("msg", "微信登录失败: " + wxResult.getString("errmsg"));
                }
            }
        } catch (Exception e) {
            log.error("微信登录异常", e);
            result.put("code", 1);
            result.put("msg", "登录异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 微信小程序登录（POST方式，自动注册）
     *
     * @param loginRequest 登录请求
     * @return 登录结果
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, String> loginRequest) {
        Map<String, Object> result = new HashMap<>();

        try {
            String code = loginRequest.get("code");
            if (code == null || code.isEmpty()) {
                throw new RuntimeException("登录凭证不能为空");
            }

            // 开发环境下，使用模拟登录
            if (devMode) {
                log.info("开发环境，使用模拟登录，code: {}", code);

                // 开发环境使用固定的openid，模拟同一个用户多次登录
                String openid = "dev_default_user"; // 固定openid，确保同一用户

                // 查询或创建用户
                User user = userService.getUserByOpenid(openid);
                if (user == null) {
                    user = new User();
                    user.setOpenid(openid);
                    user.setNickname("开发测试用户");
                    user.setAvatar("/static/default-avatar.svg");
                    user.setRole(1); // 默认为学生角色
                    user = userService.createOrUpdateUser(user);
                    log.info("开发环境：自动注册新用户: openid={}, userId={}", openid, user.getId());
                } else {
                    log.info("开发环境：用户已存在，直接登录: openid={}, userId={}", openid, user.getId());
                }

                // 构建用户信息返回
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", user.getId());
                userInfo.put("nickname", user.getNickname());
                userInfo.put("avatar", user.getAvatar());
                userInfo.put("role", user.getRole());

                // 构建返回数据
                Map<String, Object> data = new HashMap<>();
                data.put("token", openid); // 简化处理，使用openid作为token
                data.put("userInfo", userInfo);

                result.put("code", 0);
                result.put("msg", "登录成功");
                result.put("data", data);

                return result;
            }

            // 生产环境，调用微信接口
            String url = String.format(
                    "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                    appId, appSecret, code);

            Request request = new Request.Builder()
                    .url(url)
                    .build();

            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                JSONObject wxResult = JSONObject.parseObject(responseBody);

                log.info("微信登录结果: {}", wxResult);

                if (wxResult.containsKey("openid")) {
                    String openid = wxResult.getString("openid");

                    // 查询或创建用户
                    User user = userService.getUserByOpenid(openid);
                    if (user == null) {
                        // 自动注册新用户
                        user = new User();
                        user.setOpenid(openid);
                        user.setNickname("美院学生" + (System.currentTimeMillis() % 10000));
                        user.setAvatar("/static/default-avatar.svg");
                        user.setRole(1); // 默认为学生角色
                        user = userService.createOrUpdateUser(user);
                        log.info("生产环境：自动注册新用户: openid={}, userId={}", openid, user.getId());
                    } else {
                        log.info("生产环境：用户已存在，直接登录: openid={}, userId={}", openid, user.getId());
                    }

                    // 构建用户信息返回
                    Map<String, Object> userInfo = new HashMap<>();
                    userInfo.put("id", user.getId());
                    userInfo.put("nickname", user.getNickname());
                    userInfo.put("avatar", user.getAvatar());
                    userInfo.put("role", user.getRole());

                    // 构建返回数据
                    Map<String, Object> data = new HashMap<>();
                    data.put("token", openid); // 简化处理，使用openid作为token
                    data.put("userInfo", userInfo);

                    result.put("code", 0);
                    result.put("msg", "登录成功");
                    result.put("data", data);
                } else {
                    result.put("code", 1);
                    result.put("msg", "微信登录失败: " + wxResult.getString("errmsg"));
                }
            }
        } catch (Exception e) {
            log.error("登录异常", e);
            result.put("code", 1);
            result.put("msg", "登录失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 更新结果
     */
    @PostMapping("/update-user")
    public Map<String, Object> updateUser(@RequestBody User user) {
        Map<String, Object> result = new HashMap<>();

        try {
            User updatedUser = userService.createOrUpdateUser(user);

            result.put("code", 0);
            result.put("msg", "更新成功");
            result.put("data", updatedUser);
        } catch (Exception e) {
            log.error("更新用户信息异常", e);
            result.put("code", 1);
            result.put("msg", "更新失败: " + e.getMessage());
        }

        return result;
    }
    
    /**
     * 开发环境下的模拟登录
     * 用于测试，无需微信授权
     */
    @GetMapping("/dev-login")
    public Map<String, Object> devLogin(@RequestParam(defaultValue = "student") String role) {
        if (!devMode) {
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("msg", "非开发环境，禁止使用开发登录");
            return result;
        }
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建模拟用户
            String openid = "dev_" + role + "_" + System.currentTimeMillis();
            
            // 创建用户
            User user = new User();
            user.setOpenid(openid);
            user.setNickname("开发测试-" + (role.equals("teacher") ? "教师" : "学生"));
            user.setAvatar("https://placekitten.com/200/200"); // 使用占位图片
            user.setRole(role.equals("teacher") ? 2 : 1); // 根据参数设置角色
            
            user = userService.createOrUpdateUser(user);
            
            // 返回用户信息和token
            Map<String, Object> data = new HashMap<>();
            data.put("userInfo", user);
            data.put("token", openid); // 简化处理，使用openid作为token

            result.put("code", 0);
            result.put("msg", "开发登录成功");
            result.put("data", data);
        } catch (Exception e) {
            log.error("开发登录异常", e);
            result.put("code", 1);
            result.put("msg", "登录异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取当前用户信息
     * 用于刷新用户信息，特别是角色变更后
     */
    @GetMapping("/user-info")
    public Map<String, Object> getUserInfo(@RequestParam Long userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            User user = userService.getUserById(userId);
            if (user == null) {
                result.put("code", 1);
                result.put("msg", "用户不存在");
                return result;
            }

            // 构建用户信息返回
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("nickname", user.getNickname());
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("role", user.getRole());
            userInfo.put("status", user.getStatus());

            result.put("code", 0);
            result.put("msg", "获取成功");
            result.put("data", userInfo);

            log.info("获取用户信息成功: userId={}, role={}", userId, user.getRole());
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            result.put("code", 1);
            result.put("msg", "获取用户信息失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 根据用户ID获取用户信息
     * 用于获取其他用户的基本信息（如教师信息）
     */
    @GetMapping("/user/{userId}")
    public Map<String, Object> getUserById(@PathVariable Long userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            User user = userService.getUserById(userId);
            if (user == null) {
                result.put("code", 1);
                result.put("msg", "用户不存在");
                return result;
            }

            // 构建用户信息返回（只返回公开信息）
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("nickname", user.getNickname());
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("role", user.getRole());

            result.put("code", 0);
            result.put("msg", "获取成功");
            result.put("data", userInfo);

            log.info("获取用户信息成功: userId={}, role={}", userId, user.getRole());
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            result.put("code", 1);
            result.put("msg", "获取用户信息失败: " + e.getMessage());
        }

        return result;
    }

}