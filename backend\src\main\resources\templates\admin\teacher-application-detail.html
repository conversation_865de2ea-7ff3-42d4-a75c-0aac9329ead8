<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师认证申请详情 - 美院作品评价管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sidebar-width: 250px;
            --header-height: 60px;
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: block;
            padding: 15px 25px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left-color: var(--accent-color);
        }

        .sidebar-menu i {
            width: 20px;
            margin-right: 10px;
        }
        
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }
        
        .header {
            background: white;
            padding: 20px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .page-title {
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
        }
        
        .detail-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .card-header {
            background-color: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
            color: #495057;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .info-label {
            width: 120px;
            font-weight: 600;
            color: #6c757d;
            flex-shrink: 0;
        }
        
        .info-value {
            flex: 1;
            color: #495057;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-approved {
            background-color: #d1edff;
            color: #0c5460;
        }
        
        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .certificate-images {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .certificate-image {
            width: 200px;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .certificate-image:hover {
            transform: scale(1.05);
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn-back {
            background-color: #6c757d;
            border-color: #6c757d;
        }
        
        .btn-back:hover {
            background-color: #5a6268;
            border-color: #545b62;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>管理系统</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="/admin"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/users"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/artworks"><i class="fas fa-palette"></i> 作品管理</a></li>
            <li><a href="/admin/evaluations"><i class="fas fa-star"></i> 评价管理</a></li>
            <li><a href="/admin/teacher-applications" class="active"><i class="fas fa-user-graduate"></i> 教师认证</a></li>
            <li><a href="/admin/settings"><i class="fas fa-cog"></i> 系统设置</a></li>
        </ul>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 头部 -->
        <div class="header">
            <h5 class="mb-0">教师认证申请详情</h5>
            <div class="d-flex align-items-center">
                <span class="text-muted me-3">美院作品评价管理系统</span>
                <a href="/admin/logout" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-sign-out-alt"></i> 退出登录
                </a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <div th:if="${error}" class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <span th:text="${error}">错误信息</span>
            </div>

            <div th:unless="${error}">
                <h2 class="page-title">
                    教师认证申请详情
                    <span th:if="${teacherApp.status == 0}" class="status-badge status-pending ms-3">待审核</span>
                    <span th:if="${teacherApp.status == 1}" class="status-badge status-approved ms-3">已通过</span>
                    <span th:if="${teacherApp.status == 2}" class="status-badge status-rejected ms-3">已拒绝</span>
                </h2>

                <!-- 申请人基本信息 -->
                <div class="detail-card">
                    <div class="card-header">
                        <i class="fas fa-user"></i> 申请人基本信息
                    </div>
                    <div class="card-body">
                        <div class="info-row">
                            <div class="info-label">申请ID:</div>
                            <div class="info-value" th:text="${teacherApp.id}">1</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">用户ID:</div>
                            <div class="info-value" th:text="${teacherApp.userId}">10</div>
                        </div>
                        <div class="info-row" th:if="${user}">
                            <div class="info-label">用户昵称:</div>
                            <div class="info-value" th:text="${user.nickname}">用户昵称</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">真实姓名:</div>
                            <div class="info-value" th:text="${teacherApp.realName}">张三</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">身份证号:</div>
                            <div class="info-value" th:text="${teacherApp.idCard}">110101199001011234</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">工作单位:</div>
                            <div class="info-value" th:text="${teacherApp.workUnit}">某某大学</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">职位:</div>
                            <div class="info-value" th:text="${teacherApp.position}">教授</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">联系电话:</div>
                            <div class="info-value" th:text="${teacherApp.phone}">13800138000</div>
                        </div>
                        <div class="info-row" th:if="${teacherApp.email}">
                            <div class="info-label">邮箱:</div>
                            <div class="info-value" th:text="${teacherApp.email}"><EMAIL></div>
                        </div>
                    </div>
                </div>

                <!-- 认证材料 -->
                <div class="detail-card" th:if="${teacherApp.certificateImages}">
                    <div class="card-header">
                        <i class="fas fa-images"></i> 认证材料
                    </div>
                    <div class="card-body">
                        <div class="certificate-images">
                            <!-- 这里需要解析JSON数组显示图片 -->
                            <div th:text="${teacherApp.certificateImages}">认证材料图片JSON</div>
                        </div>
                    </div>
                </div>

                <!-- 申请状态信息 -->
                <div class="detail-card">
                    <div class="card-header">
                        <i class="fas fa-info-circle"></i> 申请状态信息
                    </div>
                    <div class="card-body">
                        <div class="info-row">
                            <div class="info-label">申请时间:</div>
                            <div class="info-value" th:text="${#temporals.format(teacherApp.createdAt, 'yyyy-MM-dd HH:mm:ss')}">2023-12-01 10:00:00</div>
                        </div>
                        <div class="info-row" th:if="${teacherApp.reviewedAt}">
                            <div class="info-label">审核时间:</div>
                            <div class="info-value" th:text="${#temporals.format(teacherApp.reviewedAt, 'yyyy-MM-dd HH:mm:ss')}">2023-12-01 15:00:00</div>
                        </div>
                        <div class="info-row" th:if="${teacherApp.adminRemark}">
                            <div class="info-label">审核备注:</div>
                            <div class="info-value" th:text="${teacherApp.adminRemark}">审核备注内容</div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <a href="/admin/teacher-applications" class="btn btn-secondary btn-back">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                    <button th:if="${teacherApp.status == 0}"
                            class="btn btn-success"
                            th:onclick="'reviewApplication(' + ${teacherApp.id} + ', 1)'">
                        <i class="fas fa-check"></i> 审核通过
                    </button>
                    <button th:if="${teacherApp.status == 0}"
                            class="btn btn-danger"
                            th:onclick="'reviewApplication(' + ${teacherApp.id} + ', 2)'">
                        <i class="fas fa-times"></i> 审核拒绝
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function reviewApplication(applicationId, status) {
            console.log('reviewApplication called with:', applicationId, status);

            const statusText = status === 1 ? '通过' : '拒绝';
            const remark = prompt(`请输入${statusText}理由（可选）:`);

            if (remark === null) {
                console.log('用户取消了操作');
                return; // 用户取消
            }

            if (confirm(`确定要${statusText}该申请吗？`)) {
                console.log('开始发送请求...');

                fetch(`/admin/teacher-applications/${applicationId}/review`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        status: status,
                        adminRemark: remark || ''
                    })
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.code === 0) {
                        alert(data.msg);
                        location.reload();
                    } else {
                        alert('操作失败: ' + data.msg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请重试: ' + error.message);
                });
            } else {
                console.log('用户取消了确认');
            }
        }
    </script>
</body>
</html>
